<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:vm="clr-namespace:WMS.APP.ViewModels"
             x:Class="WMS.APP.Views.inbound.ReceiveOrderDetailsPage"
             Title="收货单明细">

    <ContentPage.ToolbarItems>
        <ToolbarItem Text="新增" Command="{Binding AddNewDetailCommand}" IconImageSource="add.png" />
        <ToolbarItem Text="删除" Command="{Binding DeleteCurrentDetailCommand}" IconImageSource="delete.png" />
        <ToolbarItem Text="保存" Command="{Binding SaveDetailsCommand}" IconImageSource="save.png" />
    </ContentPage.ToolbarItems>

    <Grid RowDefinitions="Auto,*,Auto" Padding="15">
        <!-- 当前明细索引显示 -->
        <Label Text="{Binding CurrentIndexDisplay}" HorizontalOptions="Center" Margin="0,0,0,10" />

        <!-- 明细编辑区域 -->
        <VerticalStackLayout Spacing="15" Grid.Row="1" Padding="0,0,0,10">
            <Grid ColumnDefinitions="120,*" 
           RowDefinitions="Auto,Auto,Auto,Auto,Auto,Auto,Auto"
           RowSpacing="15" 
           ColumnSpacing="10">
                <Label Text="物料编码:" Grid.Row="0" Grid.Column="0" VerticalOptions="Center" />
                <Entry Text="{Binding CurrentDetail.ProductCode}" Grid.Row="0" Grid.Column="1" />

                <Label Text="物料名称:" Grid.Row="1" Grid.Column="0" VerticalOptions="Center" />
                <Entry Text="{Binding CurrentDetail.ProductName}" Grid.Row="1" Grid.Column="1" />

                <Label Text="批次号:" Grid.Row="2" Grid.Column="0" VerticalOptions="Center" />
                <Entry Text="{Binding CurrentDetail.LotNumber}" Grid.Row="2" Grid.Column="1" />

                <Label Text="计划数量:" Grid.Row="3" Grid.Column="0" VerticalOptions="Center" />
                <Entry Text="{Binding CurrentDetail.PlannedQuantity}" Keyboard="Numeric" Grid.Row="3" Grid.Column="1" />

                <Label Text="实收数量:" Grid.Row="4" Grid.Column="0" VerticalOptions="Center" />
                <Entry Text="{Binding CurrentDetail.ActualQuantity}" Keyboard="Numeric" Grid.Row="4" Grid.Column="1" />

                <Label Text="单位:" Grid.Row="5" Grid.Column="0" VerticalOptions="Center" />
                <Picker Grid.Row="5" Grid.Column="1" ItemsSource="{Binding UnitOptions}" SelectedItem="{Binding CurrentDetail.Unit}" />

                <Label Text="备注:" Grid.Row="6" Grid.Column="0" VerticalOptions="Center" />
                <Editor Text="{Binding CurrentDetail.Remarks}" Grid.Row="6" Grid.Column="1" HeightRequest="80" />
            </Grid>
        </VerticalStackLayout>

        <!-- 明细列表导航 -->
        <HorizontalStackLayout Grid.Row="2" HorizontalOptions="Center" Spacing="10" Margin="0,15,0,0">
            <Button Text="上一条" Command="{Binding NavigateUpDetailCommand}"/>
            <Button Text="下一条" Command="{Binding NavigateNxDetailCommand}"/>
        </HorizontalStackLayout>
    </Grid>
</ContentPage>


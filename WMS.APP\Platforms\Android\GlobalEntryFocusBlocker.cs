﻿using Android.Views.InputMethods;
using Android.Views;
using Android.Widget;
using Microsoft.Maui.Controls;
using Microsoft.Maui.Platform;
using Application = Android.App.Application;
using Android.Content; // ✅ 添加此命名空间引用

namespace WMS.APP.Platforms.Android
{
    public class GlobalEntryFocusBlocker : Java.Lang.Object, TextView.IOnEditorActionListener
    {
        private readonly Entry _entry;

        public GlobalEntryFocusBlocker(Entry entry)
        {
            _entry = entry;
        }

        public bool OnEditorAction(TextView v, ImeAction actionId, KeyEvent e)
        {
            if (actionId == ImeAction.Send || actionId == ImeAction.Done || actionId == ImeAction.Next)
            {
                // ✅ 正确触发 Entry 的 Completed 事件
                if (_entry is IEntryController controller)
                {
                    controller.SendCompleted();
                }
                //

                // ✅ 隐藏键盘
                var imm = Application.Context.GetSystemService(Context.InputMethodService) as InputMethodManager;
                imm?.HideSoftInputFromWindow(v.WindowToken, HideSoftInputFlags.None);

                // ✅ 隐藏光标（确保已经获得焦点）
                v.Post(async () =>
                {
                    await Task.Delay(100);
                    v.ClearFocus();          // 可选：先清焦点再重设
                    v.SetCursorVisible(false);
                    v.ShowSoftInputOnFocus = false;
                });
                return true; // ✅ 吃掉默认焦点跳转
            }

            return false;
        }
    }
}
﻿using CommunityToolkit.Maui;
using CommunityToolkit.Maui.Core;
using Microsoft.Extensions.Logging;
using WMS.APP.Common;
using WMS.APP.ViewModel;
using WMS.APP.ViewModels;
using WMS.APP.ViewModels.inbound;
using WMS.APP.ViewModels.inventory;
using WMS.APP.ViewModels.outbound;
using WMS.APP.ViewModels.system;
using WMS.APP.Views.inbound;
using WMS.APP.Views.inventory;
using WMS.APP.Views.outbound;
using WMS.APP.Views.system;
using WMS.SERVICE;
using Microsoft.Maui.Handlers;

#if ANDROID
using Android.Widget;
using WMS.APP.Platforms.Android;
#endif

namespace WMS.APP
{
    public static class MauiProgram
    {
        public static MauiApp CreateMauiApp()
        {
            var builder = MauiApp.CreateBuilder();
#if ANDROID
            WMS.APP.Platforms.Android.EntryBehavior.Init();
            // ✅ 全局拦截 Entry 的 EditorAction
            Microsoft.Maui.Handlers.EntryHandler.Mapper.AppendToMapping("BlockSystemFocus", (handler, view) =>
            {
                var nativeView = handler.PlatformView as EditText;
                if (nativeView != null && view is Entry entry)
                {
                    nativeView.SetOnEditorActionListener(new GlobalEntryFocusBlocker(entry));
                }
            });

#endif
            builder
                .UseMauiApp<App>()
                         .ConfigureMauiHandlers(handlers =>
                         {
                         })
                .UseMauiCommunityToolkit() // 使用 CommunityToolkit.Maui 扩展方法
                .UseMauiCommunityToolkitCore() // 使用 CommunityToolkit.Maui.Core 扩展方法
                .ConfigureFonts(fonts =>
                {
                    fonts.AddFont("OpenSans-Regular.ttf", "OpenSansRegular");
                    fonts.AddFont("OpenSans-Semibold.ttf", "OpenSansSemibold");
                });
#if ANDROID
            // ✅ 替换所有 Entry 控件的底层 EditText 为自定义控件
            //EntryHandler.OnPlatformViewCreated += (handler, nativeView) =>
            //{
            //    try
            //    {
            //        var context = nativeView.Context;

            //        var customView = new NoJumpEditText(context);

            //        handler.SetPlatformView(customView); // ✅ 替换平台控件

            //        customView.SetSingleLine(true);
            //        customView.SetImeOptions(Android.Views.InputMethods.ImeAction.Send);
            //        customView.ShowSoftInputOnFocus = false; // 禁止键盘
            //        customView.SetCursorVisible(false);      // 隐藏光标
            //    }
            //    catch (Exception ex)
            //    {
            //        System.Diagnostics.Debug.WriteLine($"[Entry Init Error] {ex.Message}");
            //    }
            //};
#endif
            // Register services
            builder.Services.AddSingleton<INavigationService, NavigationService>();
            builder.Services.AddScoped<IUserService, UserService>(); // Ensure WMS.SERVICE is referenced in the project  
            builder.Services.AddScoped<IInboundService, InboundService>();
            builder.Services.AddScoped<IInventoryService, InventoryService>();
            builder.Services.AddScoped<IOutboundService, OutboundService>();

            // Register ViewModels
            builder.Services.AddTransient<LoginViewModel>();
            builder.Services.AddTransient<MenuViewModel>();
            builder.Services.AddTransient<ProfileViewModel>();
            builder.Services.AddTransient<OrderViewModel>();
            builder.Services.AddTransient<OrderDetailsViewModel>();
            builder.Services.AddTransient<OrderEditViewModel>();
            builder.Services.AddTransient<PkmScanViewModel>();
            builder.Services.AddTransient<InvTransferViewModel>();
            builder.Services.AddTransient<InboundListViewModel>();
            // 注册新增的ViewModel
            builder.Services.AddTransient<IpkListViewModel>();
            builder.Services.AddTransient<IpkScanViewModel>();
            // 注册新的ViewModel
            builder.Services.AddTransient<PkmOrderViewModel>();

            //register Views
            builder.Services.AddTransient<LoginPage>();
            builder.Services.AddTransient<MainPage>();
            builder.Services.AddTransient<InMenuPage>();
            builder.Services.AddTransient<InvMenuPage>();
            builder.Services.AddTransient<OutMenuPage>();
            builder.Services.AddTransient<ProfilePage>();
            builder.Services.AddTransient<ReceiveOrderListPage>();
            builder.Services.AddTransient<ReceiveOrderDetailsPage>();
            builder.Services.AddTransient<ReceiveOrderEditPage>();
            builder.Services.AddTransient<PkmScanPage>();
            builder.Services.AddTransient<PkmNScanPage>();

            builder.Services.AddTransient<InvTransferPage>();
            builder.Services.AddTransient<InboundListPage>();
            // 注册新增的View
            builder.Services.AddTransient<IpkListPage>();
            builder.Services.AddTransient<IpkScanPage>();
            // 注册新的View
            builder.Services.AddTransient<PkmOrderListPage>();

#if DEBUG
            builder.Logging.AddDebug();
#endif

            return builder.Build();
        }
    }
}

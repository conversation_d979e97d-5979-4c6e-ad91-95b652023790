using WMS.APP.ViewModels;


namespace WMS.APP
{
    public partial class OutMenuPage : ContentPage
    {
        private readonly MenuViewModel _viewModel;

        public OutMenuPage(MenuViewModel viewModel)
        {
            InitializeComponent();
            //_serviceProvider = serviceProvider;
            //_navigation = navigationService;
            _viewModel = viewModel;
            _viewModel.MenuType = "OUT";
            _viewModel.initMenu();
            BindingContext = _viewModel;
        }

        protected override void OnAppearing()
        {
            base.OnAppearing();       
        }

    }
}

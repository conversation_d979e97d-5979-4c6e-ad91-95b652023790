﻿using Android.App;
using Android.Content;
using Android.Content.PM;
using Android.OS;
using WMS.APP.Platforms.Android;

namespace WMS.APP
{
    [Activity(Theme = "@style/Maui.SplashTheme", MainLauncher = true, LaunchMode = LaunchMode.SingleTop, ConfigurationChanges = ConfigChanges.ScreenSize | ConfigChanges.Orientation | ConfigChanges.UiMode | ConfigChanges.ScreenLayout | ConfigChanges.SmallestScreenSize | ConfigChanges.Density)]
    public class MainActivity : MauiAppCompatActivity
    {
        private ScanBarcodeReceiver? _scanReceiver;

        // 创建广播接收器实例，并传入处理扫描数据的回调方法
        // Add this property to the MainActivity class to resolve the CS0117 error.  
        public static MainActivity? Current { get; private set; }

        protected override void OnCreate(Bundle savedInstanceState)
        {
            base.OnCreate(savedInstanceState);
            Current = this;
        }

        protected override void OnResume()
        {
            base.OnResume();
            try
            {
                /*if (_scanReceiver == null)
                    _scanReceiver = new ScanBarcodeReceiver(result =>
                    {
                        // 这里可以处理扫码结果，比如通过消息中心发送到ViewModel
                        Console.WriteLine($"Scan result: {result}");
                    });*/

                Console.WriteLine("Registering scan receiver...");
                string manufacturer = Build.Manufacturer.ToLower();  // 获取设备厂商
                string model = Build.Model.ToLower();  // 获取设备型号
                //string action = "android.intent.ACTION_DECODE_DATA";
                string action = "nlscan.action.SCANNER_RESULT"; 
                // 判断是否是优博讯设备
                if (manufacturer.Contains("ubx") || model.Contains("ubx"))
                {
                    action = "android.intent.ACTION_DECODE_DATA";
                }
                // 判断是否是斑马设备
                else if (manufacturer.Contains("zebra") || model.Contains("zebra"))
                {
                    action = "com.zebra.scanner.ACTION";
                }
                // 判断是否是新大陆设备
                else if (manufacturer.Contains("newland") || model.Contains("newland"))
                {
                    action = "nlscan.action.SCANNER_RESULT";
                }
                // 判断是否是霍尼韦尔设备
                else if (manufacturer.Contains("honeywell") || model.Contains("honeywell"))
                {
                    action = "com.honeywell.decode.intent.action.EDIT_DATA";
                }
                else
                {
                    action = "com.android.server.scannerservice.broadcast";
                }

                // 实例化接收器，传入扫码回调
                _scanReceiver = new ScanBarcodeReceiver(result =>
                {
                    // 这里可以处理扫码结果，比如通过消息中心发送到ViewModel
                    Console.WriteLine($"Scan result: {result}");
                });

                var filter = new IntentFilter(action)
                {
                    Priority = 1000 // 提高优先级
                };

                RegisterReceiver(_scanReceiver, filter);
            }
            catch (Exception ex)
            {
                Console.WriteLine("Error registering receiver: " + ex.Message);
            }
        }

        protected override void OnPause()
        {
            try
            {
                if (_scanReceiver != null)
                {
                    UnregisterReceiver(_scanReceiver);
                    _scanReceiver = null;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("Error unregistering receiver: " + ex.Message);
            }
            base.OnPause();
        }
    }
}

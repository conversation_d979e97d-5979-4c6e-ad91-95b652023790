using CommunityToolkit.Mvvm.Messaging;
using System.Diagnostics;
using System.Runtime.Versioning;
using WMS.APP.Message;
using WMS.APP.ViewModels.outbound;

namespace WMS.APP.Views.outbound;

public partial class PkmScanPage : ContentPage
{
    private PkmScanViewModel ViewModel => BindingContext as PkmScanViewModel;

    public PkmScanPage()
    {
        InitializeComponent();

        // 注册扫描器结果消息
        WeakReferenceMessenger.Default.Register<ScannerResultMessage>(this, (r, m) =>
        {
            ViewModel?.HandleScanInput(m.Result);
        });

        // 初始化入口控件
        ViewModel?.InitializeEntries();
    }

    /*private void OnEntryCompleted(object sender, EventArgs e)
    {
        if (sender is Entry entry)
        {
            // 确保在完成输入后隐藏光标
            MainThread.BeginInvokeOnMainThread(() =>
            {
                //entry.Unfocus(); // 取消焦点，关闭光标 & 键盘
            });

            var fieldId = entry.StyleId ?? GetEntryFieldId(entry);
            Debug.WriteLine($"Current FieldId: {fieldId}");
            var isLastField = entry == QuantityEntry;

            if (isLastField)
            {
                ViewModel?.HandleManualCompleted();
            }
            else
            {
                ViewModel?.HandleScanInputByFieldId(fieldId, entry.Text);
            }
        }
    }*/

    private async void OnEntryCompleted(object sender, EventArgs e)
    {
        if (sender is Entry entry)
        {
            var fieldId = entry.StyleId ?? GetEntryFieldId(entry);
            var isLastField = entry == QuantityEntry;

            if (isLastField)
            {
                ViewModel?.HandleManualCompleted();
            }
            else
            {
                ViewModel?.HandleScanInputByFieldId(fieldId, entry.Text);

                // 补充实际聚焦逻辑
                /*var nextFieldId = GetNextEmptyFieldId(fieldId);
                if (nextFieldId != null)
                {
                    FocusEntryByFieldId(nextFieldId);
                }*/
            }
        }
    }


    private void OnEntryFocused(object sender, FocusEventArgs e)
    {
        if (sender is Entry entry)
        {
            Debug.WriteLine(string.Format("当前查询到的entry:{0}", entry.StyleId));
            var fieldId = GetEntryFieldId(entry);
            Debug.WriteLine(string.Format("当前查询到的ID:{0}", fieldId));
            ViewModel?.HandleScanInputByFieldId(fieldId, entry.Text);
        }
    }

    private void OnEntryUnfocused(object sender, FocusEventArgs e)
    {
        // 可以在这里添加失去焦点时的逻辑
    }

    private string GetEntryFieldId(Entry entry)
    {
        if (entry == OrderEntry) return "OrderEntry";
        if (entry == MaterialEntry) return "MaterialEntry";
        if (entry == BatchEntry) return "BatchEntry";
        if (entry == QuantityEntry) return "QuantityEntry";
        return "Unknown";
    }

    protected override void OnDisappearing()
    {
        base.OnDisappearing();
        OrderEntry.IsEnabled = false;
        Task.Delay(300);
        OrderEntry.IsEnabled = true;
        // 取消注册消息接收器
        WeakReferenceMessenger.Default.Unregister<ScannerResultMessage>(this);
    }

    private async void FocusEntryByFieldId(string fieldId)
    {
        await Task.Delay(100); // 给 Android 7.x 一些缓冲时间

        switch (fieldId)
        {
            case "OrderEntry":
                OrderEntry.Focus(); break;
            case "MaterialEntry":
                MaterialEntry.Focus(); break;
            case "BatchEntry":
                BatchEntry.Focus(); break;
            case "QuantityEntry":
                QuantityEntry.Focus(); break;
        }
    }

    private string? GetNextEmptyFieldId(string currentFieldId)
    {
        var fieldOrder = new[] { "OrderEntry", "MaterialEntry", "BatchEntry", "QuantityEntry" };
        var data = new[] { ViewModel?.OrderNumber, ViewModel?.MaterialCode, ViewModel?.BatchNumber, ViewModel?.OrderQuantity };

        int currentIndex = Array.IndexOf(fieldOrder, currentFieldId);
        if (currentIndex == -1) return null;

        for (int i = currentIndex + 1; i < fieldOrder.Length; i++)
        {
            if (string.IsNullOrEmpty(data[i]))
                return fieldOrder[i];
        }

        return null;
    }
}

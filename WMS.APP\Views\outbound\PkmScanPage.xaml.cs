using CommunityToolkit.Mvvm.Messaging;
using System.Runtime.Versioning;
using WMS.APP.Message;
using WMS.APP.ViewModels.outbound;

namespace WMS.APP.Views.outbound;

public partial class PkmScanPage : ContentPage
{
    //[SupportedOSPlatform("windows10.0.17763.0")] // Annotate the method to specify platform support
    public PkmScanPage()
    {
        InitializeComponent();

        WeakReferenceMessenger.Default.Register<ScannerResultMessage>(this, (r, m) =>
        {
            (BindingContext as PkmScanViewModel)?.HandleScanInput(m.Result);
        });

        WeakReferenceMessenger.Default.Register<ScanCompletedMessage>(this, (r, m) =>
        {
            var vm = BindingContext as PkmScanViewModel;
            if (m.IsFinalField)
                vm?.HandleManualCompleted();
            else
                vm?.HandleScanInputByFiledId(m.FromControlId, m.InputResult); // ģ��ɨ�趯��
        });

        (BindingContext as PkmScanViewModel)?.SetScanControls(<PERSON>_<PERSON>an, <PERSON>ku_<PERSON>an, <PERSON>_<PERSON>an, <PERSON>ty_<PERSON>an);
    }
}

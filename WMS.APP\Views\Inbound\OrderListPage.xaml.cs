using System.Diagnostics;
using WMS.APP.ViewModels.inbound;

namespace WMS.APP.Views.inbound
{
    public partial class ReceiveOrderListPage : ContentPage
    {
        private readonly OrderViewModel _viewModel;
        private bool _isInitialLoadComplete = false;
        
        public ReceiveOrderListPage(OrderViewModel viewModel)
        {
            InitializeComponent();
            _viewModel = viewModel;
            BindingContext = _viewModel;
        }
        
        protected override async void OnAppearing()
        {
            base.OnAppearing();
            
            // 重置初始加载标志
            _isInitialLoadComplete = false;
            
            // 每次页面显示时刷新数据
            //await Task.Delay(100); // 短暂延迟，确保UI已完全加载
            _viewModel.SearchOrderCommand.Execute(null);
            
            // 设置一个短暂延迟，确保初始加载完成
            //await Task.Delay(100);
            _isInitialLoadComplete = true;
        }

        // 滑动到底部时自动加载更多
        private async void CollectionView_RemainingItemsThresholdReached(object sender, EventArgs e)
        {
            try
            {
                // 如果初始加载尚未完成，不执行加载更多操作
                if (!_isInitialLoadComplete)
                {
                    _viewModel.IsLoadingMore = false;
                    return;
                }
                
                // 确保当前页码有效
                if (_viewModel.CurrentPage < 1)
                {
                    _viewModel.IsLoadingMore = false;
                    return;
                }
                
                await _viewModel.LoadMoreItems();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"加载更多时出错: {ex.Message}");
                // 确保即使出错也重置加载状态
                if (_viewModel != null)
                {
                    _viewModel.IsLoadingMore = false;
                }
            }
        }
    }
}








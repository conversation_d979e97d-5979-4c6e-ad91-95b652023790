namespace WMS.MODEL
{
    public class OrderDetailModel
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string OrderId { get; set; }
        public int LineNumber { get; set; }
        public string ProductCode { get; set; }
        public string ProductName { get; set; }
        public string LotNumber { get; set; }
        public decimal PlannedQuantity { get; set; }
        public decimal ActualQuantity { get; set; }
        public string Unit { get; set; } = "个";
        public string Remarks { get; set; }
    }
}
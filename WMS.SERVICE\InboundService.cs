﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using WMS.MODEL;

namespace WMS.SERVICE
{
    public class InboundService : IInboundService
    {
        private const string RECEIVE_ORDERS_KEY = "receive_orders";

        // 收货单相关方法实现
        public async Task<List<OrderModel>> GetReceiveOrdersAsync(string keyword = null)
        {
            var orders = await GetStoredReceiveOrdersAsync();

            if (!string.IsNullOrWhiteSpace(keyword))
            {
                keyword = keyword.ToLower();
                orders = orders.Where(o =>
                    o.OrderNumber.ToLower().Contains(keyword) ||
                    o.Supplier.ToLower().Contains(keyword)
                ).ToList();
            }

            return orders.OrderByDescending(o => o.CreateDate).ToList();
        }

        public async Task<OrderModel> GetReceiveOrderByIdAsync(string id)
        {
            var orders = await GetStoredReceiveOrdersAsync();
            return orders.FirstOrDefault(o => o.Id == id);
        }

        public async Task SaveReceiveOrderAsync(OrderModel order)
        {
            var orders = await GetStoredReceiveOrdersAsync();

            // 查找并更新或添加
            var existingOrder = orders.FirstOrDefault(o => o.Id == order.Id);
            if (existingOrder != null)
            {
                // 更新现有订单
                int index = orders.IndexOf(existingOrder);
                orders[index] = order;
            }
            else
            {
                // 添加新订单
                orders.Add(order);
            }

            // 保存回存储
            await SaveReceiveOrdersAsync(orders);
        }

        public async Task DeleteReceiveOrderAsync(string id)
        {
            var orders = await GetStoredReceiveOrdersAsync();
            var orderToRemove = orders.FirstOrDefault(o => o.Id == id);

            if (orderToRemove != null)
            {
                orders.Remove(orderToRemove);
                await SaveReceiveOrdersAsync(orders);
            }
        }

        public async Task<List<OrderModel>> GetReceiveOrdersPagedAsync(string keyword, int page, int pageSize)
        {
            var allOrders = await GetStoredReceiveOrdersAsync();

            // 应用搜索过滤
            if (!string.IsNullOrWhiteSpace(keyword))
            {
                keyword = keyword.ToLower();
                allOrders = allOrders.Where(o =>
                    o.OrderNumber.ToLower().Contains(keyword) ||
                    o.Supplier.ToLower().Contains(keyword)
                ).ToList();
            }

            // 排序
            allOrders = allOrders.OrderByDescending(o => o.CreateDate).ToList();

            // 应用分页
            return allOrders
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToList();
        }

        // 辅助方法 - 从存储获取所有收货单
        private async Task<List<OrderModel>> GetStoredReceiveOrdersAsync()
        {
            string json = await SecureStorage.GetAsync(RECEIVE_ORDERS_KEY);
            if (string.IsNullOrEmpty(json))
            {
                return new List<OrderModel>();
            }

            try
            {
                return JsonSerializer.Deserialize<List<OrderModel>>(json);
            }
            catch
            {
                return new List<OrderModel>();
            }
        }

        // 辅助方法 - 保存所有收货单到存储
        private async Task SaveReceiveOrdersAsync(List<OrderModel> orders)
        {
            string json = JsonSerializer.Serialize(orders);
            await SecureStorage.SetAsync(RECEIVE_ORDERS_KEY, json);
        }
        /// <summary>
        /// 清理收货单临时存储
        /// </summary>
        /// <returns>表示异步操作的任务</returns>
        public Task ClearReceiveOrdersTempStorageAsync()
        {
            try
            {
                // 移除特定键的数据
                SecureStorage.Remove(RECEIVE_ORDERS_KEY);
                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                // 记录异常或通知用户
                Console.WriteLine($"清理临时存储时出错: {ex.Message}");
                throw; // 重新抛出异常，让调用者知道发生了错误
            }
        }
    }
}

# Android 7.1 兼容性修复说明

## 问题描述
在Android 7.1及以下版本中，Complete事件光标定位总是默认定位到OrderEntry，需要实现版本兼容并支持不同的输入模式。

## 修复方案

### 1. 核心功能需求
- ✅ 默认OrderEntry高亮，键盘/光标隐藏状态
- ✅ 激光头扫描后跳转到下一个空Entry，只高亮不定位
- ✅ 人工点击Entry后高亮跳转到点击位置
- ✅ 输入文本后智能跳转逻辑
- ✅ 空输入时关闭键盘/隐藏光标，保持高亮
- ✅ 非空输入时跳转到下一个空白文本框，键盘隐藏/光标隐藏

### 2. 主要修改内容

#### PkmScanPage.xaml.cs 修改

**新增状态管理变量：**
```csharp
private bool _isManualInput = false; // 标记是否为手动输入模式
private Entry _lastFocusedEntry = null; // 记录最后获得焦点的Entry
private bool _isInitialized = false; // 标记是否已初始化
```

**扫描模式处理：**
```csharp
WeakReferenceMessenger.Default.Register<ScannerResultMessage>(this, (r, m) =>
{
    _isManualInput = false; // 扫描模式
    ViewModel?.HandleScanInput(m.Result);
    
    // 扫描后确保所有Entry失去焦点并隐藏键盘
    MainThread.BeginInvokeOnMainThread(async () =>
    {
        await HideAllKeyboardsAndCursors();
    });
});
```

**Complete事件智能处理：**
```csharp
private async void OnEntryCompleted(object sender, EventArgs e)
{
    if (sender is Entry entry)
    {
        var fieldId = GetEntryFieldId(entry);
        var inputText = entry.Text?.Trim() ?? string.Empty;
        
        // 立即隐藏键盘和光标
        await HideKeyboardAndCursor(entry);
        
        // 处理输入逻辑
        if (_isManualInput)
        {
            // 手动输入模式
            if (string.IsNullOrEmpty(inputText))
            {
                // 输入为空，保持当前高亮，键盘隐藏
                return;
            }
            else
            {
                // 输入不为空，跳转到下一个空白字段
                ViewModel?.HandleScanInputByFieldId(fieldId, inputText);
                
                // 确保跳转后的字段也隐藏键盘和光标
                await Task.Delay(100);
                await HideAllKeyboardsAndCursors();
            }
        }
        // ... 其他模式处理
    }
}
```

**焦点管理：**
```csharp
private void OnEntryFocused(object sender, FocusEventArgs e)
{
    if (sender is Entry entry)
    {
        _isManualInput = true; // 标记为手动输入模式
        _lastFocusedEntry = entry;
        
        var fieldId = GetEntryFieldId(entry);
        
        // 手动点击时，高亮跳转到点击的位置
        ViewModel?.SetActiveEntryByFieldId(fieldId);
    }
}
```

**Android兼容性处理：**
```csharp
private async Task HideKeyboardAndCursor(Entry entry)
{
    if (entry == null) return;

    try
    {
        entry.Unfocus();
        
        // Android 7.1及以下版本的兼容处理
        if (DeviceInfo.Platform == DevicePlatform.Android)
        {
#if ANDROID
            var platformView = entry.Handler?.PlatformView;
            if (platformView is Android.Widget.EditText editText)
            {
                editText.ClearFocus();
                editText.SetCursorVisible(false);
                
                var inputMethodManager = editText.Context?.GetSystemService(Android.Content.Context.InputMethodService) as Android.Views.InputMethods.InputMethodManager;
                inputMethodManager?.HideSoftInputFromWindow(editText.WindowToken, Android.Views.InputMethods.HideSoftInputFlags.None);
            }
#endif
        }
        
        await Task.Delay(50);
    }
    catch (Exception ex)
    {
        System.Diagnostics.Debug.WriteLine($"HideKeyboardAndCursor error: {ex.Message}");
    }
}
```

#### PkmScanViewModel.cs 修改

**新增方法：**
```csharp
/// <summary>
/// 根据字段ID设置活跃的Entry
/// </summary>
public void SetActiveEntryByFieldId(string fieldId)
{
    currentActiveControlId = fieldId;
    
    // 重置所有背景颜色
    OrderEntryBackgroundColor = Colors.Transparent;
    MaterialEntryBackgroundColor = Colors.Transparent;
    BatchEntryBackgroundColor = Colors.Transparent;
    QuantityEntryBackgroundColor = Colors.Transparent;
    
    // 设置当前活跃的Entry高亮
    switch (fieldId)
    {
        case "OrderEntry":
            OrderEntryBackgroundColor = Colors.Yellow;
            break;
        // ... 其他字段
    }
}
```

### 3. 工作流程

#### 扫描模式流程：
1. 扫描器广播 → `_isManualInput = false`
2. 自动填充对应字段
3. 跳转到下一个空字段（仅高亮，不获得焦点）
4. 隐藏所有键盘和光标

#### 手动输入模式流程：
1. 用户点击Entry → `_isManualInput = true`
2. 高亮跳转到点击位置
3. 用户输入完成 → Complete事件
4. 判断输入内容：
   - 空：保持高亮，隐藏键盘
   - 非空：跳转下一个空字段，隐藏键盘

### 4. 兼容性保证

#### Android 7.1及以下版本：
- 使用原生Android API强制隐藏键盘
- 直接操作EditText的CursorVisible属性
- 使用InputMethodManager确保键盘隐藏

#### Android 8.0及以上版本：
- 使用标准MAUI方法
- 依赖平台自动处理

#### 通用处理：
- 异步延迟确保操作完成
- 异常捕获防止崩溃
- 主线程执行UI操作

### 5. 测试要点

1. **扫描测试（Android 7.1）：**
   - 扫描后字段自动填充
   - 光标和键盘保持隐藏
   - 高亮正确跳转到下一个空字段

2. **手动输入测试（Android 7.1）：**
   - 点击Entry后正确高亮
   - 输入空内容后键盘隐藏，高亮保持
   - 输入内容后跳转下一个字段，键盘隐藏

3. **版本兼容测试：**
   - Android 7.1及以下版本功能正常
   - Android 8.0及以上版本功能正常
   - 不同设备间行为一致

### 6. 关键改进点

- ✅ **智能模式识别**：区分扫描和手动输入模式
- ✅ **强制键盘隐藏**：Android原生API确保兼容性
- ✅ **焦点管理优化**：避免不必要的焦点跳转
- ✅ **异步处理**：确保UI操作的流畅性
- ✅ **异常处理**：提高应用稳定性

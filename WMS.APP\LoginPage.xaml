<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="WMS.APP.LoginPage"  
             Title="登录">

    <ScrollView>
        <VerticalStackLayout Spacing="10" Padding="20">

            <Image Source="dotnet_bot.png"  HeightRequest="80" Aspect="AspectFit"
                 SemanticProperties.Description="dot net bot in a race car number eight" />
            <!-- 用户名 -->
            <Label Text="用户名:" FontSize="Medium"/>
            <Entry Placeholder="请输入用户名"
                   Text="{Binding UserName}"
                   Keyboard="Text"/>

            <!-- 密码 -->
            <Label Text="密码:" FontSize="Medium"/>
            <Entry Placeholder="请输入密码"
                   Text="{Binding Password}"
                   IsPassword="True"/>
            <HorizontalStackLayout Spacing="10" VerticalOptions="Start" x:Name="check">
                <!-- 记住密码 -->
                <CheckBox IsChecked="{Binding RememberPassword}"  HorizontalOptions="Start" Margin="0,-13,10,0" />
                <Label Text="记住密码"  FontSize="Medium" HorizontalOptions="Center" Margin="-25,-2">
                    <Label.GestureRecognizers>
                        <TapGestureRecognizer Tapped="OnRememberPasswordTapped"/>
                    </Label.GestureRecognizers>
                </Label>

                <Label Text="V-1.0.0.1"  FontSize="Medium" HorizontalOptions="End" Margin="45,-2">
                </Label>
            </HorizontalStackLayout>

            <!-- 登录按钮 -->
            <Button Text="登录" 
                    Command="{Binding LoginCommand}"
                    BackgroundColor="#2196F3"
                    TextColor="White"
                    CornerRadius="20"
                    HeightRequest="50"/>

            <!-- 关闭按钮 -->
            <Button Text="关闭" 
                    Command="{Binding CloseCommand}"
                    BackgroundColor="LightGray"
                    TextColor="Black"
                    CornerRadius="20"
                    HeightRequest="50"/>

            <!-- 状态提示 -->
            <Label Text="{Binding StatusMessage}"
                   TextColor="Red"
                   FontSize="Caption"
                   IsVisible="{Binding HasErrorMessage}"/>
        </VerticalStackLayout>
    </ScrollView>
</ContentPage>
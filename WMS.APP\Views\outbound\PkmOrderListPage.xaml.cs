using WMS.APP.ViewModels.outbound;

namespace WMS.APP.Views.outbound
{
    public partial class PkmOrderListPage : ContentPage
    {
        public readonly PkmOrderViewModel _viewModel;
        public PkmOrderListPage(PkmOrderViewModel viewModel)
        {
            InitializeComponent();
            _viewModel = viewModel;
            BindingContext = _viewModel;
        }

        protected override async void OnAppearing()
        {
            base.OnAppearing();
            //await _viewModel.LoadOrders(true);
        }
    }
}
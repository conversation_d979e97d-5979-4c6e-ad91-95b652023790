﻿using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using WMS.APP.UserControl;

namespace WMS.APP.ViewModels.outbound
{
    public partial class PkmScanViewModel : ObservableObject
    {
        [ObservableProperty]
        private string orderNumber;

        [ObservableProperty]
        private string materialCode;

        [ObservableProperty]
        private string batchNumber;

        [ObservableProperty]
        private string orderQuantity;

        public bool CanSubmit =>
            !string.IsNullOrWhiteSpace(OrderNumber)
            && !string.IsNullOrWhiteSpace(MaterialCode)
            && !string.IsNullOrWhiteSpace(BatchNumber)
            && !string.IsNullOrWhiteSpace(OrderQuantity);

        public IRelayCommand SubmitOrderCommand { get; }

        private ScanEntryView[] scanControls;

        public PkmScanViewModel()
        {
            SubmitOrderCommand = new RelayCommand(SubmitOrder, () => CanSubmit);

            this.PropertyChanged += (_, e) =>
            {
                if (e.PropertyName == nameof(OrderNumber) ||
                    e.PropertyName == nameof(MaterialCode) ||
                    e.PropertyName == nameof(BatchNumber) ||
                    e.PropertyName == nameof(OrderQuantity))
                {
                    SubmitOrderCommand.NotifyCanExecuteChanged();
                }
            };
        }

        public void SetScanControls(params ScanEntryView[] controls)
        {
            scanControls = controls;
            HighlightNextEmpty();
        }

        public void HandleScanInput(string result)
        {
            if (string.IsNullOrEmpty(OrderNumber)) OrderNumber = result;
            else if (string.IsNullOrEmpty(MaterialCode)) MaterialCode = result;
            else if (string.IsNullOrEmpty(BatchNumber)) BatchNumber = result;

            HighlightNextEmpty(); // 跳转但不提交
        }
        public void HandleScanInputByFiledId(string fileldId, string result)
        {
            if (fileldId == "Order_Scan")
            {
                OrderNumber = result;
            }
            if (fileldId == "Sku_Scan")
            {
                MaterialCode = result;
            }
            if (fileldId == "Lot_Scan")
            {
                BatchNumber = result;
            }
            if (fileldId == "Qty_Scan")
            {
                OrderQuantity = result;
            }
            HighlightNextEmpty(); // 跳转但不提交
        }

        public void HandleManualCompleted()
        {
            if (CanSubmit)
            {
                SubmitOrder();
            }
            else
            {
                HighlightNextEmpty();
            }
        }

        private void HighlightNextEmpty()
        {
            foreach (var control in scanControls)
                control.IsActive = false;

            if (string.IsNullOrEmpty(OrderNumber)) scanControls[0].IsActive = true;
            else if (string.IsNullOrEmpty(MaterialCode)) scanControls[1].IsActive = true;
            else if (string.IsNullOrEmpty(BatchNumber)) scanControls[2].IsActive = true;
            else if (string.IsNullOrEmpty(OrderQuantity)) scanControls[3].IsActive = true;
        }

        private void SubmitOrder()
        {
            Application.Current?.MainPage?.DisplayAlert("成功", "订单提交成功", "OK");

            OrderNumber = MaterialCode = BatchNumber = OrderQuantity = string.Empty;

            HighlightNextEmpty();
        }
    }
}

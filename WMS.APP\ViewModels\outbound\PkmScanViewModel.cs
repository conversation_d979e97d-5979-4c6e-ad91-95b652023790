﻿using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Diagnostics;

namespace WMS.APP.ViewModels.outbound
{
    public partial class PkmScanViewModel : ObservableObject
    {
        [ObservableProperty]
        private string orderNumber;

        [ObservableProperty]
        private string materialCode;

        [ObservableProperty]
        private string batchNumber;

        [ObservableProperty]
        private string orderQuantity;

        // 背景颜色属性用于高亮当前活跃的输入框
        [ObservableProperty]
        private Color orderEntryBackgroundColor = Colors.Transparent;

        [ObservableProperty]
        private Color materialEntryBackgroundColor = Colors.Transparent;

        [ObservableProperty]
        private Color batchEntryBackgroundColor = Colors.Transparent;

        [ObservableProperty]
        private Color quantityEntryBackgroundColor = Colors.Transparent;

        public bool CanSubmit =>
            !string.IsNullOrWhiteSpace(OrderNumber)
            && !string.IsNullOrWhiteSpace(MaterialCode)
            && !string.IsNullOrWhiteSpace(BatchNumber)
            && !string.IsNullOrWhiteSpace(OrderQuantity);

        public IRelayCommand SubmitOrderCommand { get; }

        private string currentActiveControlId; // 当前活跃控件ID跟踪

        public PkmScanViewModel()
        {
            SubmitOrderCommand = new RelayCommand(SubmitOrder, () => CanSubmit);

            this.PropertyChanged += (_, e) =>
            {
                if (e.PropertyName == nameof(OrderNumber) ||
                    e.PropertyName == nameof(MaterialCode) ||
                    e.PropertyName == nameof(BatchNumber) ||
                    e.PropertyName == nameof(OrderQuantity))
                {
                    SubmitOrderCommand.NotifyCanExecuteChanged();
                }
            };
        }

        public void InitializeEntries()
        {
            HighlightNextEmpty();
        }

        public void HandleScanInput(string result)
        {
            if (string.IsNullOrEmpty(OrderNumber)) OrderNumber = result;
            else if (string.IsNullOrEmpty(MaterialCode)) MaterialCode = result;
            else if (string.IsNullOrEmpty(BatchNumber)) BatchNumber = result;

            HighlightNextEmpty(); // 跳转但不提交
        }
        public void HandleScanInputByFieldId(string fieldId, string result)
        {
            // 记录当前活跃的控件ID
            currentActiveControlId = fieldId;
            Debug.WriteLine($"Current load FieldId: {currentActiveControlId}");
            switch (fieldId)
            {
                case "OrderEntry":
                    OrderNumber = result;
                    break;
                case "MaterialEntry":
                    MaterialCode = result;
                    break;
                case "BatchEntry":
                    BatchNumber = result;
                    break;
                case "QuantityEntry":
                    OrderQuantity = result;
                    break;
            }
            HighlightNextEmpty(); // 跳转但不提交
        }

        public void HandleManualCompleted()
        {
            if (CanSubmit)
            {
                SubmitOrder();
            }
            else
            {
                HighlightNextEmpty();
            }
        }

        private void HighlightNextEmpty()
        {
            // 重置所有背景颜色
            OrderEntryBackgroundColor = Colors.Transparent;
            MaterialEntryBackgroundColor = Colors.Transparent;
            BatchEntryBackgroundColor = Colors.Transparent;
            QuantityEntryBackgroundColor = Colors.Transparent;

            // 改进焦点管理逻辑
            // 如果有当前活跃控件，尝试从当前控件的下一个开始查找
            if (!string.IsNullOrEmpty(currentActiveControlId))
            {
                var currentIndex = GetControlIndex(currentActiveControlId);

                // 从当前控件的下一个开始查找空字段
                for (int i = currentIndex; i < 4; i++)
                {
                    if (IsControlEmpty(i))
                    {
                        SetActiveControl(i);
                        return;
                    }
                }

                // 如果后面没有空字段，从头开始查找
                for (int i = 0; i <= currentIndex + 1; i++)
                {
                    if (IsControlEmpty(i))
                    {
                        SetActiveControl(i);
                        return;
                    }
                }
            }
            else
            {
                // 默认逻辑：从第一个空字段开始
                if (string.IsNullOrEmpty(OrderNumber)) SetActiveControl(0);
                else if (string.IsNullOrEmpty(MaterialCode)) SetActiveControl(1);
                else if (string.IsNullOrEmpty(BatchNumber)) SetActiveControl(2);
                else if (string.IsNullOrEmpty(OrderQuantity)) SetActiveControl(3);
            }
        }

        private int GetControlIndex(string controlId)
        {
            return controlId switch
            {
                "OrderEntry" => 0,
                "MaterialEntry" => 1,
                "BatchEntry" => 2,
                "QuantityEntry" => 3,
                _ => 0
            };
        }

        private bool IsControlEmpty(int index)
        {
            return index switch
            {
                0 => string.IsNullOrEmpty(OrderNumber),
                1 => string.IsNullOrEmpty(MaterialCode),
                2 => string.IsNullOrEmpty(BatchNumber),
                3 => string.IsNullOrEmpty(OrderQuantity),
                _ => false
            };
        }

        private void SetActiveControl(int index)
        {
            switch (index)
            {
                case 0:
                    OrderEntryBackgroundColor = Colors.Yellow;
                    break;
                case 1:
                    MaterialEntryBackgroundColor = Colors.Yellow;
                    break;
                case 2:
                    BatchEntryBackgroundColor = Colors.Yellow;
                    break;
                case 3:
                    QuantityEntryBackgroundColor = Colors.Yellow;
                    break;
            }
        }

        private void SubmitOrder()
        {
            Application.Current?.MainPage?.DisplayAlert("成功", "订单提交成功", "OK");

            OrderNumber = MaterialCode = BatchNumber = OrderQuantity = string.Empty;
            currentActiveControlId = null; // 重置当前活跃控件ID

            HighlightNextEmpty();
        }
    }
}

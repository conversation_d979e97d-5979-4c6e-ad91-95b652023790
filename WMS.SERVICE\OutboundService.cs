﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using WMS.MODEL;

namespace WMS.SERVICE
{
    public class OutboundService : IOutboundService
    {
        private readonly List<PkmOrderModel> _mockOrders;
        private readonly Random _random = new Random();

        public OutboundService()
        {
            _mockOrders = GenerateMockOrders();
        }

        public async Task<List<PkmOrderModel>> GetOutboundOrdersAsync(
            string systemOrderNo = null,
            string customerOrderNo = null,
            DateTime? startDate = null,
            DateTime? endDate = null,
            int page = 1,
            int pageSize = 10)
        {
            // 模拟网络延迟
            await Task.Delay(800);

            var query = _mockOrders.AsQueryable();

            // 应用筛选条件
            if (!string.IsNullOrWhiteSpace(systemOrderNo))
                query = query.Where(o => o.orderNo.Contains(systemOrderNo));

            if (!string.IsNullOrWhiteSpace(customerOrderNo))
                query = query.Where(o => o.orderCon.Contains(customerOrderNo));

            if (startDate.HasValue)
                query = query.Where(o => o.orderDate >= startDate.Value);

            if (endDate.HasValue)
                query = query.Where(o => o.orderDate <= endDate.Value);

            // 分页
            return query
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToList();
        }

        private List<PkmOrderModel> GenerateMockOrders()
        {
            var orders = new List<PkmOrderModel>();
            var orderTypes = new[] { "正常出库", "退货出库", "调拨出库" };
            var owners = new[] { "苹果公司", "华为公司", "小米公司", "OPPO公司", "VIVO公司" };

            for (int i = 1; i <= 50; i++)
            {
                var order = new PkmOrderModel
                {
                    orderNo = $"SO{DateTime.Now:yyyyMMdd}{i:D4}",
                    orderCon = $"CSO{_random.Next(10000, 99999)}",
                    orderType = orderTypes[_random.Next(orderTypes.Length)],
                    owner = owners[_random.Next(owners.Length)],
                    orderDate = DateTime.Now.AddDays(-_random.Next(0, 30)),
                    orderRemark = $"这是第{i}个测试订单的备注信息"
                };
                orders.Add(order);
            }

            return orders.OrderByDescending(o => o.orderDate).ToList();
        }
    }
}

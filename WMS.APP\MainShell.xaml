<?xml version="1.0" encoding="UTF-8" ?>
<Shell
    x:Class="WMS.APP.MainShell"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:d="http://schemas.microsoft.com/dotnet/2021/maui/design"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:local="clr-namespace:WMS.APP"
    xmlns:localview="clr-namespace:WMS.APP.Views.system"
    mc:Ignorable="d"
    Shell.TabBarTitleColor = "#512DA8"
    Shell.TabBarUnselectedColor = "DarkOrchid"
    Shell.TabBarBackgroundColor = "Transparent">
    <!--
        The overall app visual hierarchy is defined here, along with navigation.
        Ensure atleast a Flyout item or a TabBar is defined for Shell to work
    -->
    <Shell.Resources>
        <!--
        Define the resources to be used within this Shell visual hierarchy
    -->
        <Style TargetType="Shell">
            <Setter Property="Shell.TabBarBackgroundColor" Value="White"/>
            <Setter Property="Shell.TabBarForegroundColor" Value="Gray"/>
            <Setter Property="Shell.TabBarTitleColor" Value="Black"/>
            <Setter Property="Shell.TabBarUnselectedColor" Value="SpringGreen"/>
            <Setter Property="Shell.TabBarDisabledColor" Value="DarkGray"/>
            <Setter Property="Shell.TabBarTitleColor" Value="DarkCyan"/>
        </Style>
    </Shell.Resources>
    <TabBar>
        <ShellContent Title="入库管理" Icon="inn.png" ContentTemplate="{DataTemplate local:InMenuPage}" />
        <ShellContent Title="出库管理" Icon="out_2.png" ContentTemplate="{DataTemplate local:OutMenuPage}" />
        <ShellContent Title="库存管理" Icon="inv2.png" ContentTemplate="{DataTemplate local:InvMenuPage}" />
        <ShellContent Title="用户信息" Icon="user.png" ContentTemplate="{DataTemplate localview:ProfilePage}" />
    </TabBar>
    <!--
        When the Flyout is visible this defines the content to display in the flyout.
        FlyoutDisplayOptions="AsMultipleItems" will create a separate flyout item for each child element
    -->
    <!--
        Uncomment the below section to enable Shell Flyout item(s)
        And ensure appropriate page definitions are available for it work as expected
    -->
    <!--
    <FlyoutItem Title="Home">
        <ShellContent ContentTemplate="{DataTemplate local:HomePage}" Route="home" />
    </FlyoutItem>
    <FlyoutItem Title="Search">
        <ShellContent ContentTemplate="{DataTemplate local:SearchPage}" Route="search" />
    </FlyoutItem>
    -->
    <!--
        When the Flyout is visible this will be a menu item you can tie a click behavior to
    -->
    <!--
        Uncomment the below section to enable Menu item(s)
        And ensure appropriate page definitions are available for it work as expected
    -->
    <!--
    <MenuItem Clicked="OnMenuItemClicked" Text="Logout" />
    -->
    <!--
        TabBar lets you define content that won't show up in a Flyout menu. When this content is active
        the flyout menu won't be available. This is useful for creating areas of the application where
        you don't want users to be able to navigate away from. If you would like to navigate to this
        content you can do so by calling
        await Shell.Current.GoToAsync("//login");
    -->
    <!--
        Uncomment the below section to enable TabBar item(s)
        And ensure appropriate page definitions are available for it work as expected
    -->
    <!--
    <TabBar>
        <ShellContent ContentTemplate="{DataTemplate local:LoginPage}" Route="login" />
    </TabBar>
    -->
    <!-- Optional Templates
    // These may be provided inline as below or as separate classes.

    // This header appears at the top of the Flyout
    <Shell.FlyoutHeaderTemplate>
        <DataTemplate>
            <Grid>ContentHere</Grid>
        </DataTemplate>
    </Shell.FlyoutHeaderTemplate>

    // ItemTemplate is for ShellItems as displayed in a Flyout
    <Shell.ItemTemplate>
        <DataTemplate>
            <ContentView>
                Bindable Properties: Title, Icon
            </ContentView>
        </DataTemplate>
    </Shell.ItemTemplate>

    // MenuItemTemplate is for MenuItems as displayed in a Flyout
    <Shell.MenuItemTemplate>
        <DataTemplate>
            <ContentView>
                Bindable Properties: Text, Icon
            </ContentView>
        </DataTemplate>
    </Shell.MenuItemTemplate>

    // This footer appears at the bottom of the Flyout
    <Shell.FlyoutFooterTemplate>
        <DataTemplate>
            <Grid>ContentHere</Grid>
        </DataTemplate>
    </Shell.FlyoutFooterTemplate>
    -->
</Shell>

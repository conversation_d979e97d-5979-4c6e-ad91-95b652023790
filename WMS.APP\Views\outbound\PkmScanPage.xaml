<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="WMS.APP.Views.outbound.PkmScanPage"
             xmlns:vm="clr-namespace:WMS.APP.ViewModels.outbound"
             Title="分拣扫描">

    <ContentPage.BindingContext>
        <vm:PkmScanViewModel />
    </ContentPage.BindingContext>

    <Grid RowDefinitions="*,Auto" Padding="10">
        <!-- 内容区，支持滚动 -->
        <ScrollView Grid.Row="0">
            <VerticalStackLayout Spacing="15">

                <!-- 订单号输入区 -->
                <VerticalStackLayout Padding="5">
                    <Label Text="订单号" FontSize="15" FontAttributes="Bold"/>
                    <Entry x:Name="OrderEntry"
                           Text="{Binding OrderNumber, Mode=TwoWay}"
                           StyleId="OrderEntry"
                           Completed="OnEntryCompleted"
                           Focused="OnEntryFocused"
                           Unfocused="OnEntryUnfocused"
                           ReturnType="Next"
                           FontSize="15"
                           HeightRequest="45"
                           Placeholder="请输入或扫码"
                           BackgroundColor="{Binding OrderEntryBackgroundColor}"/>
                </VerticalStackLayout>

                <!-- 物料编号输入区 -->
                <VerticalStackLayout Padding="5">
                    <Label Text="物料编号" FontSize="15" FontAttributes="Bold"/>
                    <Entry x:Name="MaterialEntry"
                           StyleId="MaterialEntry"
                           Text="{Binding MaterialCode, Mode=TwoWay}"
                           Completed="OnEntryCompleted"
                           Focused="OnEntryFocused"
                           Unfocused="OnEntryUnfocused"
                           ReturnType="Next"
                           FontSize="15"
                           HeightRequest="45"
                           Placeholder="请输入或扫码"
                           BackgroundColor="{Binding MaterialEntryBackgroundColor}"/>
                </VerticalStackLayout>

                <!-- 批次号输入区 -->
                <VerticalStackLayout Padding="5">
                    <Label Text="批次号" FontSize="15" FontAttributes="Bold"/>
                    <Entry x:Name="BatchEntry"
                           Text="{Binding BatchNumber, Mode=TwoWay}"
                           StyleId="BatchEntry"
                           Completed="OnEntryCompleted"
                           Focused="OnEntryFocused"
                           Unfocused="OnEntryUnfocused"
                           ReturnType="Next"
                           FontSize="15"
                           HeightRequest="45"
                           Placeholder="请输入或扫码"
                           BackgroundColor="{Binding BatchEntryBackgroundColor}"/>
                </VerticalStackLayout>

                <!-- 订单数量输入区 -->
                <VerticalStackLayout Padding="5">
                    <Label Text="订单数量" FontSize="15" FontAttributes="Bold"/>
                    <Entry x:Name="QuantityEntry"
                           Text="{Binding OrderQuantity, Mode=TwoWay}"
                           StyleId="QuantityEntry"
                           Completed="OnEntryCompleted"
                           Focused="OnEntryFocused"
                           Unfocused="OnEntryUnfocused"
                           ReturnType="Done"
                           Keyboard="Numeric"
                           FontSize="15"
                           HeightRequest="45"
                           Placeholder="请输入或扫码"
                           BackgroundColor="{Binding QuantityEntryBackgroundColor}"/>
                </VerticalStackLayout>

            </VerticalStackLayout>
        </ScrollView>

        <!-- 固定在底部的提交按钮 -->
        <Button Grid.Row="1"
                Text="提交订单"
                Command="{Binding SubmitOrderCommand}"
                IsEnabled="{Binding CanSubmit}"
                Margin="0,16,0,0"/>
    </Grid>
</ContentPage>
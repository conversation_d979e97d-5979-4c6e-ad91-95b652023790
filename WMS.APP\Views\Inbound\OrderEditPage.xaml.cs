using System.Diagnostics;
using WMS.APP.ViewModels.inbound;
using WMS.MODEL;
using WMS.SERVICE;

namespace WMS.APP.Views.inbound
{
    [QueryProperty(nameof(OrderId), "orderId")]
    public partial class ReceiveOrderEditPage : ContentPage
    {
        private readonly OrderEditViewModel _viewModel;
        private readonly IInboundService _dataService;

        public ReceiveOrderEditPage(OrderEditViewModel viewModel, IInboundService dataService)
        {
            try
            {
                InitializeComponent();
                _viewModel = viewModel;
                _dataService = dataService;
                BindingContext = _viewModel;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"页面初始化错误: {ex.Message}");
                throw;
            }
        }

        private string _orderId;
        public string OrderId
        {
            get => _orderId;
            set
            {
                _orderId = value;
                LoadOrder();
            }
        }

        private async void LoadOrder()
        {
            try
            {
                if (!string.IsNullOrEmpty(_orderId))
                {
                    // 如果是新建订单（使用特殊ID标记）
                    if (_orderId == "new")
                    {
                        var newOrder = new OrderModel
                        {
                            OrderNumber = GenerateOrderNumber(),
                            CreateDate = DateTime.Now,
                            Status = "新建"
                        };
                        _viewModel.Initialize(newOrder);
                    }
                    else
                    {
                        // 加载现有订单
                        var order = await _dataService.GetReceiveOrderByIdAsync(_orderId);
                        if (order != null)
                        {
                            _viewModel.Initialize(order);
                        }
                        else
                        {
                            await Shell.Current.DisplayAlert("错误", $"未找到ID为 {_orderId} 的订单", "确定");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"加载订单错误: {ex.Message}");
                await Shell.Current.DisplayAlert("错误", $"加载订单失败: {ex.Message}", "确定");
            }
        }

        private string GenerateOrderNumber()
        {
            return $"RCV{DateTime.Now:yyyyMMddHHmmss}";
        }
    }
}


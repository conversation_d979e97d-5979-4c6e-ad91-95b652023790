﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using WMS.MODEL;

namespace WMS.SERVICE
{
    public interface IOutboundService
    {
        Task<List<PkmOrderModel>> GetOutboundOrdersAsync(
            string systemOrderNo = null,
            string customerOrderNo = null,
            DateTime? startDate = null,
            DateTime? endDate = null,
            int page = 1,
            int pageSize = 10);
    }
}

<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodel="WMS.APP.ViewModels.MenuViewModel"
             x:Class="WMS.APP.InvMenuPage"
             Title="库存管理">
    <!-- 九宫格布局 -->
    <CollectionView ItemsSource="{Binding MenuItems}"
                    SelectionMode="None">
        <CollectionView.ItemsLayout>
            <GridItemsLayout Orientation="Vertical" 
                            Span="3" />
            <!-- 每行3列 -->
        </CollectionView.ItemsLayout>

        <CollectionView.ItemTemplate>
            <DataTemplate>
                <Frame Padding="0" 
                       CornerRadius="10" 
                       HeightRequest="80"
                       WidthRequest="100"
                       Margin="10"
                       HasShadow="True">
                    <Grid RowDefinitions="Auto,Auto">
                        <!-- 图标 -->
                        <Image Source="{Binding Icon}" 
                               HeightRequest="40"
                               HorizontalOptions="Center"
                               Grid.Row="0"/>
                        <!-- 标题 -->
                        <Label Text="{Binding Title}" 
                               HorizontalOptions="Center"
                               VerticalOptions="End"
                               FontSize="16"
                               Grid.Row="1"/>

                        <!-- 点击手势 -->
                        <Grid.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding Source={RelativeSource AncestorType={x:Type viewmodel:MenuViewModel}}, Path=NavigateCommand}"
                                                  CommandParameter="{Binding TargetPageType}"/>
                        </Grid.GestureRecognizers>
                    </Grid>
                </Frame>
            </DataTemplate>
        </CollectionView.ItemTemplate>
        
    </CollectionView>
   
</ContentPage>